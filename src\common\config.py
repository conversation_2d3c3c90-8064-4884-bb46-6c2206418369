"""
简化的配置模块
"""

class SecuritySettings:
    """安全配置"""
    def __init__(self):
        self.cors_origins = ["*"]  # 开发环境允许所有来源


class ServiceSettings:
    """服务配置"""
    def __init__(self):
        self.orchestrator_port = 8080  # 改为8080端口避免冲突


class MonitoringSettings:
    """监控配置"""
    def __init__(self):
        self.log_level = "INFO"


class Settings:
    """简化的配置类"""
    
    def __init__(self):
        self.version = "0.1.0"
        self.environment = "development"
        
        # 子配置
        self.security = SecuritySettings()
        self.service = ServiceSettings()
        self.monitoring = MonitoringSettings()
    
    @property
    def is_development(self):
        return self.environment == "development"
    
    @property
    def is_production(self):
        return self.environment == "production"

# 全局配置实例
settings = Settings()

def get_settings():
    """获取配置实例"""
    return settings 