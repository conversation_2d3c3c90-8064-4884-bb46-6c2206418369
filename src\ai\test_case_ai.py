"""
AI增强的测试用例生成器

使用大模型生成智能化的测试用例
"""

from typing import Dict, List, Optional, Any
import json
import asyncio

from src.ai.llm_client import LLMClient, LLMProvider
from src.ai.prompt_manager import PromptManager, PromptType
from src.test_case.models import TestCaseDefinition, GenerationContext, GenerationOptions
from src.common.logger import get_logger

logger = get_logger(__name__)


class TestCaseAI:
    """
    AI增强的测试用例生成器
    
    结合LLM和规则引擎生成高质量测试用例
    """
    
    def __init__(self):
        self.llm_client = LLMClient()
        self.prompt_manager = PromptManager()
        self.is_initialized = False
    
    async def initialize(
        self,
        provider: LLMProvider = LLMProvider.MOCK,
        model_name: str = "gpt-3.5-turbo",
        **kwargs
    ) -> None:
        """
        初始化AI模块
        
        Args:
            provider: LLM提供商
            model_name: 模型名称
            **kwargs: 其他配置参数
        """
        try:
            logger.info("Initializing TestCaseAI")
            
            await self.llm_client.initialize(
                provider=provider,
                model_name=model_name,
                **kwargs
            )
            
            self.is_initialized = True
            logger.info("TestCaseAI initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize TestCaseAI: {e}")
            raise
    
    async def generate_test_cases(
        self,
        requirement_text: str,
        context: GenerationContext,
        options: GenerationOptions
    ) -> List[TestCaseDefinition]:
        """
        使用AI生成测试用例
        
        Args:
            requirement_text: 需求描述
            context: 生成上下文
            options: 生成选项
            
        Returns:
            生成的测试用例列表
        """
        if not self.is_initialized:
            logger.warning("TestCaseAI not initialized, using fallback generation")
            return await self._fallback_generation(requirement_text, context, options)
        
        try:
            logger.info("Generating test cases with AI", requirement_length=len(requirement_text))
            
            # 准备提示词变量
            prompt_variables = {
                'requirement_text': requirement_text,
                'domain': context.domain or "通用应用",
                'system_type': context.system_type or "Web应用",
                'crypto_enabled': options.crypto_enabled,
                'max_test_cases': options.max_test_cases,
                'include_security_tests': options.include_security_tests
            }
            
            # 获取系统提示词和用户提示词
            system_prompt = self.prompt_manager.get_system_prompt(PromptType.TEST_CASE_GENERATION)
            user_prompt = self.prompt_manager.get_prompt(
                PromptType.TEST_CASE_GENERATION,
                prompt_variables
            )
            
            # 调用LLM生成
            response = await self.llm_client.chat(
                messages=[{'role': 'user', 'content': user_prompt}],
                system_prompt=system_prompt
            )
            
            # 解析响应
            test_cases = await self._parse_ai_response(response['content'], context, options)
            
            logger.info(
                "AI test case generation completed",
                generated_count=len(test_cases),
                model=response.get('model', 'unknown')
            )
            
            return test_cases
            
        except Exception as e:
            logger.error(f"AI test case generation failed: {e}")
            # 降级到规则引擎生成
            return await self._fallback_generation(requirement_text, context, options)
    
    async def _parse_ai_response(
        self,
        content: str,
        context: GenerationContext,
        options: GenerationOptions
    ) -> List[TestCaseDefinition]:
        """
        解析AI响应内容
        
        Args:
            content: AI响应内容
            context: 生成上下文
            options: 生成选项
            
        Returns:
            解析后的测试用例列表
        """
        try:
            # 尝试解析JSON响应
            content = content.strip()
            
            # 移除可能的markdown代码块标记
            if content.startswith('```json'):
                content = content[7:]
            elif content.startswith('```'):
                content = content[3:]
            if content.endswith('```'):
                content = content[:-3]
            
            # 解析JSON
            data = json.loads(content.strip())
            
            # 提取测试用例数据
            test_cases_data = data.get('test_cases', [])
            if not isinstance(test_cases_data, list):
                raise ValueError("Invalid test_cases format")
            
            # 转换为TestCaseDefinition对象
            test_cases = []
            for i, case_data in enumerate(test_cases_data):
                try:
                    test_case = TestCaseDefinition(
                        title=case_data.get('title', f'测试用例 {i+1}'),
                        description=case_data.get('description', ''),
                        type=case_data.get('type', 'functional'),
                        priority=case_data.get('priority', 'medium'),
                        tags=case_data.get('tags', []),
                        steps=case_data.get('steps', []),
                        expected_result=case_data.get('expected_result', ''),
                        test_data=case_data.get('test_data', {}),
                        crypto_requirements=case_data.get('crypto_requirements')
                    )
                    test_cases.append(test_case)
                except Exception as e:
                    logger.warning(f"Failed to parse test case {i+1}: {e}")
                    continue
            
            return test_cases
            
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse AI response as JSON: {e}")
            logger.error(f"Raw content: {content[:500]}...")
            
            # 尝试从文本中提取测试用例信息
            return await self._extract_from_text(content, context, options)
    
    async def _extract_from_text(
        self,
        content: str,
        context: GenerationContext,
        options: GenerationOptions
    ) -> List[TestCaseDefinition]:
        """
        从文本内容中提取测试用例信息
        
        Args:
            content: 文本内容
            context: 生成上下文
            options: 生成选项
            
        Returns:
            提取的测试用例列表
        """
        test_cases = []
        
        # 简单的文本解析逻辑
        lines = content.split('\n')
        current_case = {}
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
            
            # 检测标题行
            if '测试用例' in line or 'Test Case' in line.title():
                if current_case:
                    test_case = self._create_test_case_from_dict(current_case)
                    if test_case:
                        test_cases.append(test_case)
                current_case = {'title': line}
            
            # 检测其他字段
            elif line.startswith('描述') or line.startswith('Description'):
                current_case['description'] = line.split(':', 1)[-1].strip()
            elif line.startswith('步骤') or line.startswith('Steps'):
                current_case['steps'] = [line.split(':', 1)[-1].strip()]
            elif line.startswith('预期') or line.startswith('Expected'):
                current_case['expected_result'] = line.split(':', 1)[-1].strip()
        
        # 处理最后一个用例
        if current_case:
            test_case = self._create_test_case_from_dict(current_case)
            if test_case:
                test_cases.append(test_case)
        
        return test_cases
    
    def _create_test_case_from_dict(self, case_dict: Dict[str, Any]) -> Optional[TestCaseDefinition]:
        """从字典创建测试用例"""
        try:
            return TestCaseDefinition(
                title=case_dict.get('title', '未命名测试用例'),
                description=case_dict.get('description', ''),
                type=case_dict.get('type', 'functional'),
                priority=case_dict.get('priority', 'medium'),
                tags=case_dict.get('tags', []),
                steps=case_dict.get('steps', []),
                expected_result=case_dict.get('expected_result', ''),
                test_data=case_dict.get('test_data', {}),
                crypto_requirements=case_dict.get('crypto_requirements')
            )
        except Exception as e:
            logger.warning(f"Failed to create test case from dict: {e}")
            return None
    
    async def _fallback_generation(
        self,
        requirement_text: str,
        context: GenerationContext,
        options: GenerationOptions
    ) -> List[TestCaseDefinition]:
        """
        降级生成方法（规则引擎）
        
        Args:
            requirement_text: 需求描述
            context: 生成上下文
            options: 生成选项
            
        Returns:
            生成的测试用例列表
        """
        logger.info("Using fallback rule-based generation")
        
        test_cases = []
        
        # 基于关键词的简单规则生成
        keywords = requirement_text.lower()
        
        # 登录相关测试用例
        if '登录' in keywords or 'login' in keywords:
            test_cases.extend([
                TestCaseDefinition(
                    title="用户登录-正常场景",
                    description="验证用户使用正确的用户名和密码能够成功登录",
                    type="functional",
                    priority="high",
                    tags=["login", "authentication"],
                    steps=[
                        "1. 打开登录页面",
                        "2. 输入正确的用户名",
                        "3. 输入正确的密码",
                        "4. 点击登录按钮"
                    ],
                    expected_result="登录成功，跳转到主页面",
                    test_data={"username": "test_user", "password": "Test123!"}
                ),
                TestCaseDefinition(
                    title="用户登录-错误密码",
                    description="验证使用错误密码无法登录",
                    type="negative",
                    priority="high",
                    tags=["login", "security"],
                    steps=[
                        "1. 打开登录页面",
                        "2. 输入正确的用户名",
                        "3. 输入错误的密码",
                        "4. 点击登录按钮"
                    ],
                    expected_result="登录失败，显示错误提示",
                    test_data={"username": "test_user", "password": "wrong_password"}
                )
            ])
        
        # 注册相关测试用例
        if '注册' in keywords or 'register' in keywords:
            test_cases.append(
                TestCaseDefinition(
                    title="用户注册-正常场景",
                    description="验证用户能够成功注册新账号",
                    type="functional",
                    priority="high",
                    tags=["register", "signup"],
                    steps=[
                        "1. 打开注册页面",
                        "2. 输入用户名",
                        "3. 输入邮箱",
                        "4. 输入密码",
                        "5. 确认密码",
                        "6. 点击注册按钮"
                    ],
                    expected_result="注册成功，发送确认邮件",
                    test_data={
                        "username": "new_user",
                        "email": "<EMAIL>",
                        "password": "Test123!"
                    }
                )
            )
        
        # 如果没有生成任何用例，创建默认用例
        if not test_cases:
            test_cases.append(
                TestCaseDefinition(
                    title="基础功能测试",
                    description=f"基于需求的基础功能验证: {requirement_text[:100]}...",
                    type="functional",
                    priority="medium",
                    tags=["basic"],
                    steps=["1. 执行基础功能操作", "2. 验证结果"],
                    expected_result="功能正常工作",
                    test_data={}
                )
            )
        
        # 限制用例数量
        if len(test_cases) > options.max_test_cases:
            test_cases = test_cases[:options.max_test_cases]
        
        return test_cases
    
    async def analyze_requirement(self, requirement_text: str) -> Dict[str, Any]:
        """
        使用AI分析需求
        
        Args:
            requirement_text: 需求文本
            
        Returns:
            需求分析结果
        """
        if not self.is_initialized:
            return {"error": "AI not initialized"}
        
        try:
            prompt_variables = {'requirement_text': requirement_text}
            
            system_prompt = self.prompt_manager.get_system_prompt(PromptType.REQUIREMENT_ANALYSIS)
            user_prompt = self.prompt_manager.get_prompt(
                PromptType.REQUIREMENT_ANALYSIS,
                prompt_variables
            )
            
            response = await self.llm_client.chat(
                messages=[{'role': 'user', 'content': user_prompt}],
                system_prompt=system_prompt
            )
            
            return {
                'analysis': response['content'],
                'model': response.get('model', 'unknown'),
                'usage': response.get('usage', {})
            }
            
        except Exception as e:
            logger.error(f"Requirement analysis failed: {e}")
            return {"error": str(e)}
    
    def is_available(self) -> bool:
        """检查AI服务是否可用"""
        return self.is_initialized and self.llm_client.is_available() 