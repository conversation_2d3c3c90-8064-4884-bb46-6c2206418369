"""
加密客户端

提供统一的加密/解密、签名/验签接口，支持多种算法和Vault集成
"""

import asyncio
import time
from typing import Dict, Optional, Any, Union
from uuid import UUID

from src.common.logger import get_logger
from .models import (
    CryptoRequest,
    CryptoResponse,
    CryptoOperation,
    EncryptionConfig,
    SignatureConfig,
    VaultConfig,
    CryptoStats,
    EncryptionAlgorithm,
    SignatureAlgorithm,
    HashAlgorithm,
)

logger = get_logger(__name__)


class CryptoClient:
    """
    加密客户端
    
    提供统一的加密/解密、签名/验签、哈希等功能接口
    支持Vault集成进行密钥管理
    """
    
    def __init__(self, vault_config: Optional[VaultConfig] = None):
        self.logger = get_logger(__name__)
        self._vault_config = vault_config
        self._initialized = False
        self._vault_client = None
        self._stats = CryptoStats()
        
        # 缓存密钥 (生产环境应该有适当的过期和刷新机制)
        self._key_cache: Dict[str, Any] = {}
        
    async def initialize(self) -> None:
        """初始化客户端"""
        try:
            self.logger.info("Initializing CryptoClient")
            
            # 初始化Vault连接
            if self._vault_config:
                await self._initialize_vault()
            
            # 验证算法支持
            await self._validate_algorithms()
            
            self._initialized = True
            self.logger.info("CryptoClient initialized successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize CryptoClient: {e}")
            raise
    
    async def cleanup(self) -> None:
        """清理资源"""
        try:
            self.logger.info("Cleaning up CryptoClient")
            
            # 清理密钥缓存
            self._key_cache.clear()
            
            # 清理Vault连接
            if self._vault_client:
                # TODO: 关闭Vault连接
                pass
                
            self._initialized = False
            self.logger.info("CryptoClient cleaned up successfully")
            
        except Exception as e:
            self.logger.error(f"Error during CryptoClient cleanup: {e}")
    
    async def process_request(self, request: CryptoRequest) -> CryptoResponse:
        """
        处理加密请求
        
        Args:
            request: 加密请求
            
        Returns:
            CryptoResponse: 处理结果
        """
        start_time = time.time()
        
        try:
            self.logger.info(
                f"Processing crypto request",
                request_id=str(request.request_id),
                operation=request.operation
            )
            
            # 更新统计
            self._stats.total_operations += 1
            
            # 根据操作类型分发处理
            if request.operation == CryptoOperation.ENCRYPT:
                result = await self._encrypt(request)
            elif request.operation == CryptoOperation.DECRYPT:
                result = await self._decrypt(request)
            elif request.operation == CryptoOperation.SIGN:
                result = await self._sign(request)
            elif request.operation == CryptoOperation.VERIFY:
                result = await self._verify(request)
            elif request.operation == CryptoOperation.HASH:
                result = await self._hash(request)
            else:
                raise ValueError(f"Unsupported operation: {request.operation}")
            
            processing_time = (time.time() - start_time) * 1000  # 转换为毫秒
            
            response = CryptoResponse(
                request_id=request.request_id,
                success=True,
                operation=request.operation,
                result=result,
                processing_time=processing_time,
                metadata={"input_size": len(str(request.data))}
            )
            
            # 更新统计
            self._stats.successful_operations += 1
            self._stats.operations_by_type[request.operation] = (
                self._stats.operations_by_type.get(request.operation, 0) + 1
            )
            
            self.logger.info(
                f"Crypto request processed successfully",
                request_id=str(request.request_id),
                operation=request.operation,
                processing_time=processing_time
            )
            
            return response
            
        except Exception as e:
            processing_time = (time.time() - start_time) * 1000
            
            self.logger.error(
                f"Crypto request failed",
                request_id=str(request.request_id),
                operation=request.operation,
                error=str(e)
            )
            
            # 更新统计
            self._stats.failed_operations += 1
            
            return CryptoResponse(
                request_id=request.request_id,
                success=False,
                operation=request.operation,
                error_message=str(e),
                error_code="CRYPTO_ERROR",
                processing_time=processing_time
            )
    
    async def get_stats(self) -> CryptoStats:
        """获取统计信息"""
        # 计算平均处理时间
        if self._stats.total_operations > 0:
            # 这里应该维护一个处理时间的历史记录来计算真实的平均值
            # 暂时返回估算值
            self._stats.average_processing_time = 50.0  # 假设50ms平均处理时间
            
        return self._stats
    
    async def _encrypt(self, request: CryptoRequest) -> str:
        """加密数据"""
        if not request.encryption_config:
            raise ValueError("Encryption config is required for encrypt operation")
        
        config = request.encryption_config
        
        # TODO: 实现真实的加密逻辑
        # 这里先返回模拟的加密结果
        
        self.logger.info(
            f"Encrypting data with {config.algorithm}",
            key_id=config.key_config.key_id,
            algorithm=config.algorithm
        )
        
        # 更新算法统计
        self._stats.operations_by_algorithm[config.algorithm] = (
            self._stats.operations_by_algorithm.get(config.algorithm, 0) + 1
        )
        
        # 模拟加密结果 (base64编码的密文)
        import base64
        data_str = str(request.data)
        encrypted_data = base64.b64encode(f"ENCRYPTED_{data_str}".encode()).decode()
        
        return encrypted_data
    
    async def _decrypt(self, request: CryptoRequest) -> str:
        """解密数据"""
        if not request.encryption_config:
            raise ValueError("Encryption config is required for decrypt operation")
        
        config = request.encryption_config
        
        # TODO: 实现真实的解密逻辑
        
        self.logger.info(
            f"Decrypting data with {config.algorithm}",
            key_id=config.key_config.key_id,
            algorithm=config.algorithm
        )
        
        # 更新算法统计
        self._stats.operations_by_algorithm[config.algorithm] = (
            self._stats.operations_by_algorithm.get(config.algorithm, 0) + 1
        )
        
        # 模拟解密结果
        import base64
        try:
            encrypted_str = str(request.data)
            decoded_data = base64.b64decode(encrypted_str).decode()
            if decoded_data.startswith("ENCRYPTED_"):
                return decoded_data[10:]  # 移除前缀
            else:
                return decoded_data
        except Exception:
            return str(request.data)
    
    async def _sign(self, request: CryptoRequest) -> str:
        """签名数据"""
        if not request.signature_config:
            raise ValueError("Signature config is required for sign operation")
        
        config = request.signature_config
        
        # TODO: 实现真实的签名逻辑
        
        self.logger.info(
            f"Signing data with {config.algorithm}",
            key_id=config.key_config.key_id,
            algorithm=config.algorithm,
            hash_algorithm=config.hash_algorithm
        )
        
        # 更新算法统计
        algo_key = f"{config.algorithm}+{config.hash_algorithm}"
        self._stats.operations_by_algorithm[algo_key] = (
            self._stats.operations_by_algorithm.get(algo_key, 0) + 1
        )
        
        # 模拟签名结果
        import base64
        import hashlib
        
        data_str = str(request.data)
        hash_obj = hashlib.sha256(data_str.encode())
        signature = base64.b64encode(f"SIGNATURE_{hash_obj.hexdigest()}".encode()).decode()
        
        return signature
    
    async def _verify(self, request: CryptoRequest) -> bool:
        """验证签名"""
        if not request.signature_config:
            raise ValueError("Signature config is required for verify operation")
        
        config = request.signature_config
        
        # TODO: 实现真实的验签逻辑
        
        self.logger.info(
            f"Verifying signature with {config.algorithm}",
            key_id=config.key_config.key_id,
            algorithm=config.algorithm,
            hash_algorithm=config.hash_algorithm
        )
        
        # 更新算法统计
        algo_key = f"{config.algorithm}+{config.hash_algorithm}"
        self._stats.operations_by_algorithm[algo_key] = (
            self._stats.operations_by_algorithm.get(algo_key, 0) + 1
        )
        
        # 模拟验签结果 (总是返回True用于测试)
        return True
    
    async def _hash(self, request: CryptoRequest) -> str:
        """计算哈希"""
        if not request.hash_algorithm:
            raise ValueError("Hash algorithm is required for hash operation")
        
        algorithm = request.hash_algorithm
        
        # TODO: 实现更多哈希算法
        
        self.logger.info(
            f"Hashing data with {algorithm}"
        )
        
        # 更新算法统计
        self._stats.operations_by_algorithm[algorithm] = (
            self._stats.operations_by_algorithm.get(algorithm, 0) + 1
        )
        
        # 实现基本的哈希计算
        import hashlib
        import base64
        
        data_str = str(request.data)
        
        if algorithm == HashAlgorithm.SHA256:
            hash_obj = hashlib.sha256(data_str.encode())
        elif algorithm == HashAlgorithm.SHA1:
            hash_obj = hashlib.sha1(data_str.encode())
        elif algorithm == HashAlgorithm.MD5:
            hash_obj = hashlib.md5(data_str.encode())
        else:
            # 对于SM3等暂不支持的算法，返回SHA256结果
            hash_obj = hashlib.sha256(data_str.encode())
        
        return hash_obj.hexdigest()
    
    async def _initialize_vault(self) -> None:
        """初始化Vault连接"""
        try:
            self.logger.info("Initializing Vault connection")
            
            # TODO: 实现真实的Vault连接
            # import hvac
            # self._vault_client = hvac.Client(
            #     url=self._vault_config.vault_url,
            #     token=self._vault_config.vault_token
            # )
            
            # 模拟Vault初始化成功
            self.logger.info("Vault connection initialized (simulated)")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize Vault connection: {e}")
            raise
    
    async def _validate_algorithms(self) -> None:
        """验证算法支持"""
        try:
            self.logger.info("Validating algorithm support")
            
            # 检查加密算法支持
            supported_encryption = [algo.value for algo in EncryptionAlgorithm]
            self.logger.info(f"Supported encryption algorithms: {supported_encryption}")
            
            # 检查签名算法支持
            supported_signature = [algo.value for algo in SignatureAlgorithm]
            self.logger.info(f"Supported signature algorithms: {supported_signature}")
            
            # 检查哈希算法支持
            supported_hash = [algo.value for algo in HashAlgorithm]
            self.logger.info(f"Supported hash algorithms: {supported_hash}")
            
            self.logger.info("Algorithm validation completed")
            
        except Exception as e:
            self.logger.error(f"Algorithm validation failed: {e}")
            raise
    
    async def _get_key_from_vault(self, key_id: str) -> Optional[str]:
        """从Vault获取密钥"""
        try:
            # 检查缓存
            if key_id in self._key_cache:
                return self._key_cache[key_id]
            
            # TODO: 从Vault获取密钥
            # if self._vault_client:
            #     response = self._vault_client.secrets.kv.v2.read_secret_version(
            #         path=f"keys/{key_id}"
            #     )
            #     key_data = response['data']['data']['key']
            #     self._key_cache[key_id] = key_data
            #     return key_data
            
            # 模拟密钥获取
            mock_key = f"mock_key_for_{key_id}"
            self._key_cache[key_id] = mock_key
            return mock_key
            
        except Exception as e:
            self.logger.error(f"Failed to get key from Vault: {e}")
            return None 