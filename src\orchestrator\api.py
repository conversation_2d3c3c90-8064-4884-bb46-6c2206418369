"""
Orchestrator API 路由

提供测试用例生成、脚本生成、执行管理等RESTful接口
"""

from typing import Dict
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException
from fastapi.responses import StreamingResponse

from src.common.logger import get_logger
from src.orchestrator.service import OrchestratorService
from src.orchestrator.models import (
    TestCaseGenerationRequest,
    TestCaseGenerationResponse,
    ScriptGenerationRequest,
    ScriptGenerationResponse,
    ExecutionRequest,
    ExecutionResponse,
    ExecutionStatus,
    SessionContext,
    FeedbackData,
)

logger = get_logger(__name__)
router = APIRouter(tags=["orchestrator"])


def get_orchestrator_service() -> OrchestratorService:
    """获取Orchestrator服务实例的依赖注入函数"""
    # 这个函数会被FastAPI的依赖注入系统调用
    # 实际的服务实例会通过应用状态获取
    # 在main.py中会通过dependency_overrides重写这个函数
    raise HTTPException(
        status_code=503, 
        detail="Orchestrator service not available"
    )


@router.post("/sessions", response_model=SessionContext)
async def create_session(
    orchestrator: OrchestratorService = Depends(get_orchestrator_service)
) -> SessionContext:
    """
    创建新的会话上下文
    
    Returns:
        SessionContext: 新创建的会话信息
    """
    try:
        logger.info("Attempting to create session")
        session = await orchestrator.create_session()
        logger.info("Session created", session_id=str(session.session_id))
        return session
    except Exception as e:
        logger.error("Failed to create session", error=str(e), exc_info=True)
        raise HTTPException(status_code=500, detail=f"Failed to create session: {str(e)}")


@router.get("/sessions/{session_id}", response_model=SessionContext)
async def get_session(
    session_id: UUID,
    orchestrator: OrchestratorService = Depends(get_orchestrator_service)
) -> SessionContext:
    """
    获取会话信息
    
    Args:
        session_id: 会话ID
        
    Returns:
        SessionContext: 会话信息
    """
    try:
        session = await orchestrator.get_session(session_id)
        if not session:
            raise HTTPException(status_code=404, detail="Session not found")
        return session
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to get session", session_id=str(session_id), error=str(e))
        raise HTTPException(status_code=500, detail="Failed to get session")


@router.post("/test-cases/generate", response_model=TestCaseGenerationResponse)
async def generate_test_cases(
    request: TestCaseGenerationRequest,
    orchestrator: OrchestratorService = Depends(get_orchestrator_service)
) -> TestCaseGenerationResponse:
    """
    生成测试用例
    
    Args:
        request: 测试用例生成请求
        background_tasks: 后台任务
        orchestrator: Orchestrator服务实例
        
    Returns:
        TestCaseGenerationResponse: 测试用例生成响应
    """
    try:
        logger.info(
            "Starting test case generation",
            session_id=str(request.session_id),
            requirement_length=len(request.requirement_text)
        )
        
        response = await orchestrator.generate_test_cases(request)
        
        logger.info(
            "Test case generation completed",
            session_id=str(request.session_id),
            task_id=str(response.task_id),
            test_case_count=len(response.test_cases)
        )
        
        return response
        
    except Exception as e:
        logger.error(
            "Failed to generate test cases",
            session_id=str(request.session_id),
            error=str(e)
        )
        raise HTTPException(status_code=500, detail="Failed to generate test cases")


@router.post("/scripts/generate", response_model=ScriptGenerationResponse)
async def generate_scripts(
    request: ScriptGenerationRequest,
    orchestrator: OrchestratorService = Depends(get_orchestrator_service)
) -> ScriptGenerationResponse:
    """
    生成测试脚本
    
    Args:
        request: 脚本生成请求
        background_tasks: 后台任务
        orchestrator: Orchestrator服务实例
        
    Returns:
        ScriptGenerationResponse: 脚本生成响应
    """
    try:
        logger.info(
            "Starting script generation",
            session_id=str(request.session_id),
            test_case_count=len(request.test_case_ids)
        )
        
        response = await orchestrator.generate_scripts(request)
        
        logger.info(
            "Script generation completed",
            session_id=str(request.session_id),
            task_id=str(response.task_id),
            script_count=len(response.scripts)
        )
        
        return response
        
    except Exception as e:
        logger.error(
            "Failed to generate scripts",
            session_id=str(request.session_id),
            error=str(e)
        )
        raise HTTPException(status_code=500, detail="Failed to generate scripts")


@router.post("/executions", response_model=ExecutionResponse)
async def start_execution(
    request: ExecutionRequest,
    orchestrator: OrchestratorService = Depends(get_orchestrator_service)
) -> ExecutionResponse:
    """
    启动测试执行
    
    Args:
        request: 执行请求
        background_tasks: 后台任务
        orchestrator: Orchestrator服务实例
        
    Returns:
        ExecutionResponse: 执行响应
    """
    try:
        logger.info(
            "Starting test execution",
            session_id=str(request.session_id),
            script_count=len(request.script_ids)
        )
        
        response = await orchestrator.start_execution(request)
        
        logger.info(
            "Test execution started",
            session_id=str(request.session_id),
            execution_id=str(response.execution_id)
        )
        
        return response
        
    except Exception as e:
        logger.error(
            "Failed to start execution",
            session_id=str(request.session_id),
            error=str(e)
        )
        raise HTTPException(status_code=500, detail="Failed to start execution")


@router.get("/executions/{execution_id}/status", response_model=ExecutionStatus)
async def get_execution_status(
    execution_id: UUID,
    orchestrator: OrchestratorService = Depends(get_orchestrator_service)
) -> ExecutionStatus:
    """
    获取执行状态
    
    Args:
        execution_id: 执行ID
        orchestrator: Orchestrator服务实例
        
    Returns:
        ExecutionStatus: 执行状态
    """
    try:
        status = await orchestrator.get_execution_status(execution_id)
        if not status:
            raise HTTPException(status_code=404, detail="Execution not found")
        return status
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            "Failed to get execution status",
            execution_id=str(execution_id),
            error=str(e)
        )
        raise HTTPException(status_code=500, detail="Failed to get execution status")


@router.get("/executions/{execution_id}/logs")
async def get_execution_logs(
    execution_id: UUID,
    orchestrator: OrchestratorService = Depends(get_orchestrator_service)
) -> StreamingResponse:
    """
    获取执行日志流
    
    Args:
        execution_id: 执行ID
        orchestrator: Orchestrator服务实例
        
    Returns:
        StreamingResponse: 日志流响应
    """
    try:
        log_stream = await orchestrator.get_execution_logs(execution_id)
        if not log_stream:
            raise HTTPException(status_code=404, detail="Execution not found")
            
        return StreamingResponse(
            log_stream,
            media_type="text/plain",
            headers={"Cache-Control": "no-cache"}
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            "Failed to get execution logs",
            execution_id=str(execution_id),
            error=str(e)
        )
        raise HTTPException(status_code=500, detail="Failed to get execution logs")


@router.post("/feedback")
async def submit_feedback(
    feedback_data: FeedbackData,
    orchestrator: OrchestratorService = Depends(get_orchestrator_service)
) -> Dict[str, str]:
    """
    提交反馈数据

    Args:
        feedback_data: 反馈数据
        orchestrator: Orchestrator服务实例

    Returns:
        Dict[str, str]: 处理结果
    """
    try:
        await orchestrator.process_feedback(feedback_data.model_dump())
        logger.info("Feedback submitted successfully", feedback_id=str(feedback_data.feedback_id))
        return {"status": "success", "message": "Feedback submitted successfully"}
    except Exception as e:
        logger.error("Failed to submit feedback", error=str(e))
        raise HTTPException(status_code=500, detail="Failed to submit feedback")


@router.get("/health")
async def health_check() -> Dict[str, str]:
    """
    API健康检查
    
    Returns:
        Dict[str, str]: 健康状态
    """
    return {"status": "healthy", "component": "orchestrator-api"} 